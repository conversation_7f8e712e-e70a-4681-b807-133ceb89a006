import * as sql from 'mssql';
import * as dotenv from 'dotenv';
import { join } from 'path';

// 加载 .env 文件
dotenv.config({ path: join(process.cwd(), '.env') });

async function testConnection() {
  const config = {
    user: process.env.DB_USERNAME || 'sa',
    password: process.env.DB_PASSWORD || 'your_password',
    server: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '1433', 10),
    database: process.env.DB_DATABASE || 'your_database',
    options: {
      encrypt: process.env.DB_ENCRYPT === 'true',
      enableArithAbort: process.env.DB_ENABLE_ARITH_ABORT === 'true',
    },
  };

  try {
    await sql.connect(config);
    console.log('✅ 数据库连接成功');
    const result = await sql.query`SELECT 1 AS alive`;
    console.log('Query result:', result);
    sql.close();
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    sql.close();
  }
}

testConnection();