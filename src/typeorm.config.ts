import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import * as dotenv from 'dotenv';
import { join } from 'path';

// 加载 .env 文件
dotenv.config({ path: join(process.cwd(), '.env') });

console.log('Database config:', {
  type: process.env.DB_TYPE,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  username: process.env.DB_USERNAME,
  database: process.env.DB_DATABASE,
});

export default {
  type: process.env.DB_TYPE as 'mssql',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '1433', 10),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: process.env.DB_SYNCHRONIZE === 'true',
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    enableArithAbort: process.env.DB_ENABLE_ARITH_ABORT === 'true',
  },
  logging: process.env.DB_LOGGING === 'true',
  connectTimeout: 10000, // 增加连接超时设置
} as TypeOrmModuleOptions;
