import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import typeormConfig from './typeorm.config';
import { ProjectStatusController } from './controller/project-status.controller';
import { ProjectStatusService } from './service/project-status.service';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ProjectStatusDistribution } from './entity/project-status.entity';

@Module({
  imports: [
    TypeOrmModule.forRoot(typeormConfig), // 配置 TypeORM 数据库连接
    TypeOrmModule.forFeature([ProjectStatusDistribution]), // 添加对ProjectStatusDistribution实体的支持
  ],
  controllers: [AppController, ProjectStatusController],
  providers: [AppService, ProjectStatusService],
})
export class AppModule {}
