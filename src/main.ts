import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const port = process.env.PORT ?? 3001;

  // 启用 CORS 并指定允许的源
  app.enableCors({
    origin: 'http://localhost:3000', // 明确只允许你的前端域名
    credentials: true, // 允许携带凭证（如 cookies）
  });

  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
}
bootstrap();
