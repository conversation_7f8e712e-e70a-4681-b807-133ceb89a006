import { Controller, Get } from '@nestjs/common';
import { ProjectStatusService } from '../service/project-status.service';
import { ProjectStatusDistributionDto } from '../dto/project-status.dto';

@Controller('project-status')
export class ProjectStatusController {
  constructor(private readonly projectStatusService: ProjectStatusService) {}

  @Get('distribution')
  async getProjectStatusDistribution(): Promise<
    ProjectStatusDistributionDto[]
  > {
    return this.projectStatusService.getProjectStatusDistribution();
  }
}
