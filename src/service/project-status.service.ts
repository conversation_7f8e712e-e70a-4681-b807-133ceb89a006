import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProjectStatusDistributionDto } from '../dto/project-status.dto';
import { ProjectStatusDistribution } from '../entity/project-status.entity';

@Injectable()
export class ProjectStatusService {
  constructor(
    @InjectRepository(ProjectStatusDistribution)
    private readonly projectStatusRepo: Repository<ProjectStatusDistribution>,
  ) {}

  async getProjectStatusDistribution(): Promise<
    ProjectStatusDistributionDto[]
  > {
    const result = await this.projectStatusRepo.query(
      `SELECT Prj_ProjectStatus.description AS 项目状态, COUNT(*) AS 项目数量
       FROM Prj_ProjectInfo
                LEFT JOIN Prj_ProjectStatus ON Prj_ProjectInfo.status = Prj_ProjectStatus.id
       GROUP BY Prj_ProjectStatus.description
       ORDER BY Prj_ProjectStatus.description`,
    );

    return result.map((item) => ({
      name: item.项目状态,
      value: item.项目数量,
    }));
  }
}
