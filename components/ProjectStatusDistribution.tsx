import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, Legend } from 'recharts';
import { useEffect, useState } from 'react';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A28EF5', '#FF6666'];

export interface ProjectStatus {
  项目状态: string;
  项目数量: number;
  value: number;
}

interface ProjectStatusDistributionProps {
  data: ProjectStatus[];
}

const ProjectStatusDistribution: React.FC<ProjectStatusDistributionProps> = ({ data }) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!data || !Array.isArray(data) || data.length === 0) {
    return <div>没有可用的数据来显示项目状态分布。</div>;
  }

  const total = data.reduce((sum, item) => sum + item.项目数量, 0);

  return (
    <div className="w-full h-full p-4 flex flex-col">
      <h2 className="text-lg font-semibold mb-4 flex-shrink-0">项目状态分布</h2>
      <div className="flex-1 flex min-h-0">
        <div className="w-1/2 flex items-center justify-center">
          {isClient ? (
            <div className="w-full h-full flex items-center justify-center">
              <PieChart width="100%" height="100%" className="max-w-full max-h-full">
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ 项目状态, percent }) => `${项目状态}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius="40%"
                  fill="#8884d8"
                  dataKey="项目数量"
                >
                  {data.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </div>
          ) : null}
        </div>
        <div className="w-1/2 overflow-auto">
          <table className="min-w-full leading-normal table-auto">
            <thead>
              <tr>
                <th className="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-1/3">
                  项目状态
                </th>
                <th className="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-1/3">
                  数量
                </th>
                <th className="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-1/3">
                  占比
                </th>
              </tr>
            </thead>
            <tbody>
              {data.map((item, index) => (
                <tr key={index}>
                  <td className="px-5 py-2 border-b border-gray-200 bg-white text-sm truncate">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: COLORS[index % COLORS.length] }}></div>
                      <div className="ml-2 truncate">{item.项目状态}</div>
                    </div>
                  </td>
                  <td className="px-5 py-2 border-b border-gray-200 bg-white text-sm text-center">
                    {item.项目数量}
                  </td>
                  <td className="px-5 py-2 border-b border-gray-200 bg-white text-sm text-center">
                    {(item.项目数量 / total * 100).toFixed(2)}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
};

export default ProjectStatusDistribution;