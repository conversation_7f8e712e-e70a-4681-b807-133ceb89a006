import React from 'react';

interface CardContainerProps {
  children: React.ReactNode;
  title?: string;
  className?: string;
  showTitle?: boolean;
}

const CardContainer: React.FC<CardContainerProps> = ({ 
  children, 
  title,
  className = "",
  showTitle = false
}) => {
  return (
    <div className={`
      bg-white 
      rounded-lg 
      shadow-md 
      overflow-hidden 
      flex 
      flex-col 
      h-full 
      w-full
      ${className}
    `}>
      {showTitle && title && (
        <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex-shrink-0">
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        </div>
      )}
      <div className="flex-1 min-h-0 overflow-hidden">
        {children}
      </div>
    </div>
  );
};

export default CardContainer;
