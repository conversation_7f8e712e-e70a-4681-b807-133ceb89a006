import React from 'react';

interface EmptyCardProps {
  title?: string;
  description?: string;
  className?: string;
}

const EmptyCard: React.FC<EmptyCardProps> = ({
  title = "待开发模块",
  description = "此区域预留给未来功能模块",
  className = ""
}) => {
  return (
    <div className={`
      w-full h-full
      border-2 border-dashed border-gray-300
      bg-gray-50
      rounded-lg
      flex flex-col items-center justify-center
      p-4
      hover:border-gray-400 hover:bg-gray-100
      transition-colors duration-200
      ${className}
    `}>
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
          <svg 
            className="w-8 h-8 text-gray-400" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 6v6m0 0v6m0-6h6m-6 0H6" 
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-600 mb-2">{title}</h3>
        <p className="text-sm text-gray-500">{description}</p>
      </div>
    </div>
  );
};

export default EmptyCard;
