import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import Image from "next/image";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "中德华建项目综合看板",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-100 h-screen overflow-hidden`}
      >
        <div className="h-screen bg-gray-100 flex flex-col">
          {/* Header */}
          <header className="bg-white shadow-sm p-4 flex justify-between items-center flex-shrink-0">
            <div className="flex items-center">
              <Image
                src="/images/zdhj-logo.jpg"
                alt="Company Logo"
                width={180}
                height={40}
                style={{ backgroundColor: 'rgba(255, 255, 255, 0.5)', filter: 'saturate(150%)' }}
              />
              <h1 className="ml-4 text-2xl font-bold">中德华建项目综合看板</h1>
            </div>
            <nav className="flex-grow flex justify-end space-x-6">
              <a href="/dashboard" className="text-blue-600 hover:text-blue-800 font-medium text-lg">项目总览</a>
              <a href="/resources" className="text-gray-700 hover:text-blue-600 font-medium text-lg">资源管理</a>
              <a href="/reports" className="text-gray-700 hover:text-blue-600 font-medium text-lg">风险评估</a>
              <a href="/documents" className="text-gray-700 hover:text-blue-600 font-medium text-lg">文档中心</a>
            </nav>
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-hidden">
            {children}
          </main>

          {/* Footer */}
          <footer className="bg-white border-t p-2 text-center text-sm text-gray-500 flex-shrink-0">
            © 2025 中德华建技术有限公司. 版权所有.
          </footer>
        </div>
      </body>
    </html>
  );
}
