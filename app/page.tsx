"use client";

import { useEffect, useState } from 'react';
import ProjectStatusDistribution from '../components/ProjectStatusDistribution';
import EmptyCard from '../components/EmptyCard';

export default function Home() {
  const [data, setData] = useState<ProjectStatusData[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/project-status/distribution`);
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`网络响应失败: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        // 转换数据格式以匹配ProjectStatus类型
        const transformedData = result.map((item: { name: string; value: number }) => {
          if (!item.name || !item.value) {
            throw new Error('Invalid data format: missing name or value');
          }
          return {
            项目状态: item.name,
            项目数量: item.value
          };
        });
        setData(transformedData);
      } catch (error) {
        console.error('数据获取失败:', error);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="h-full p-4 flex flex-col">
      {/* 九宫格布局 - 自动占满剩余空间 */}
      <div className="flex-1 grid grid-cols-3 gap-4 min-h-0">
          {/* 第一行 */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <div className="flex-1 min-h-0">
              <ProjectStatusDistribution data={data} />
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <EmptyCard
              title="项目进度统计"
              description="显示各项目的进度情况"
              className="flex-1"
            />
          </div>
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <EmptyCard
              title="资源使用情况"
              description="展示人力和设备资源分配"
              className="flex-1"
            />
          </div>

          {/* 第二行 */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <EmptyCard
              title="任务分配"
              description="当前任务分配和负责人"
              className="flex-1"
            />
          </div>
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <EmptyCard
              title="预算执行"
              description="项目预算使用和剩余情况"
              className="flex-1"
            />
          </div>
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <EmptyCard
              title="风险监控"
              description="项目风险识别和预警"
              className="flex-1"
            />
          </div>

          {/* 第三行 */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <EmptyCard
              title="质量指标"
              description="项目质量评估和指标"
              className="flex-1"
            />
          </div>
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <EmptyCard
              title="时间轴"
              description="项目时间线和里程碑"
              className="flex-1"
            />
          </div>
          <div className="bg-white rounded-lg shadow-md overflow-hidden flex flex-col">
            <EmptyCard
              title="团队绩效"
              description="团队工作效率和绩效"
              className="flex-1"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// 替换为正确的类型定义
interface ProjectStatusData {
  项目状态: string;
  项目数量: number;
  value: number;
}
