"use client";

import { useEffect, useState } from 'react';
import ProjectStatusDistribution from '../components/ProjectStatusDistribution';

export default function Home() {
  const [data, setData] = useState<ProjectStatusData[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/project-status/distribution`);
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`网络响应失败: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        // 转换数据格式以匹配ProjectStatus类型
        const transformedData = result.map((item: { name: string; value: number }) => {
          if (!item.name || !item.value) {
            throw new Error('Invalid data format: missing name or value');
          }
          return {
            项目状态: item.name,
            项目数量: item.value
          };
        });
        setData(transformedData);
      } catch (error) {
        console.error('数据获取失败:', error);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="w-full flex space-x-4">
      <div className="flex-shrink-0">
        <ProjectStatusDistribution data={data} />
      </div>
      <div className="flex-1 grid grid-cols-2 gap-4">
        <div>其他组件2</div>
        <div>其他组件3</div>
      </div>
    </div>

  );
}

// 替换为正确的类型定义
interface ProjectStatusData {
  项目状态: string;
  项目数量: number;
  value: number;
}
