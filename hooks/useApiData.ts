import { useState, useEffect } from 'react';

interface UseApiDataOptions<T> {
  url: string;
  transform?: (data: any) => T[];
  dependencies?: any[];
}

interface UseApiDataResult<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useApiData<T>({
  url,
  transform,
  dependencies = []
}: UseApiDataOptions<T>): UseApiDataResult<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${url}`);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`网络响应失败: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      
      // 如果提供了转换函数，使用它；否则直接使用原始数据
      const transformedData = transform ? transform(result) : result;
      setData(transformedData);
    } catch (error) {
      console.error('数据获取失败:', error);
      setError(error instanceof Error ? error.message : '数据获取失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, dependencies);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}
